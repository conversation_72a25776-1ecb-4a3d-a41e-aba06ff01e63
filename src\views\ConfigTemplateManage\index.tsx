import React, { useEffect, useRef, useState } from 'react';
import { message, Popconfirm } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { CommonForm, CommonTable, useTableData } from '@jd/x-coreui';
import { ConfigTemplateApi } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';
import { SearchConfig, TableConfig } from './utils/columns';
import { formatDateToSecond } from '@/utils/formatTime';
import { RootState } from '@/redux/store';
import {
  saveSearchValues,
  removeSearchValues,
} from '@/redux/reducers/searchform';
import './index.scss';
import { useNavigate } from 'react-router-dom';

const ConfigTemplateManage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const historySearchValues = useSelector(
    (state: RootState) => state.searchform,
  );

  const initSearchCondition = {
    searchForm: {
      productKey: undefined,
      productModelNo: undefined,
      templateNo: undefined,
      templateName: undefined,
      createUser: undefined,
      createTime: undefined,
      createTimeStart: undefined,
      createTimeEnd: undefined,
      enable: undefined,
    },
    pageNum: 1,
    pageSize: 10,
  };

  const [searchCondition, setSearchCondition] = useState(() => {
    return historySearchValues.searchValues
      ? historySearchValues.searchValues
      : initSearchCondition;
  });

  const configTemplateApi = useRef(new ConfigTemplateApi());
  const [tableKey, setTableKey] = useState('');
  const [searchConfig, setSearchConfig] = useState(SearchConfig);

  const { tableData, loading } = useTableData(
    {
      ...searchCondition,
      searchForm: {
        ...searchCondition.searchForm,
      },
    },
    configTemplateApi.current.getConfigTemplatePage,
    tableKey,
  );

  // 页面初始化时获取产品列表
  useEffect(() => {
    fetchProductList();
  }, []);

  // 获取产品列表
  const fetchProductList = async () => {
    try {
      const res = await configTemplateApi.current.getProductList();
      if (res.code === HttpStatusCode.Success) {
        // 转换数据格式为下拉选项格式
        const productOptions =
          res.data?.map((item: any) => ({
            label: item.productName,
            value: item.productKey,
          })) || [];

        // 更新产品下拉框选项
        const productField = SearchConfig.fields.find(
          (field: any) => field.fieldName === 'productKey',
        );
        if (productField) {
          productField.options = productOptions;
          setSearchConfig({ ...searchConfig });
        }

        // 如果没有历史搜索值，设置第一个产品为默认值
        if (
          (!historySearchValues?.routeName ||
            historySearchValues.routeName !== location.pathname) &&
          productOptions.length > 0
        ) {
          const firstProduct = productOptions[0];
          const newSearchCondition = {
            ...searchCondition,
            searchForm: {
              ...searchCondition.searchForm,
              productKey: firstProduct,
            },
          };
          setSearchCondition(newSearchCondition);
        }
      } else {
        message.error(res.message || '获取产品列表失败');
      }
    } catch (error) {
      message.error('获取产品列表失败');
    }
  };

  const onSearchClick = (values: any) => {
    const newSearchCondition = {
      searchForm: values,

      pageNum: 1,
      pageSize: 10,
    };
    setTableKey(Date.now().toString());
    setSearchCondition(newSearchCondition);

    // 保存搜索条件到 store
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: newSearchCondition,
      }),
    );
  };

  const onResetClick = () => {
    setTableKey(Date.now().toString());
    setSearchCondition(initSearchCondition);

    // 清除 store 中的搜索条件
    dispatch(
      removeSearchValues({
        routeName: null,
        searchValues: initSearchCondition,
      }),
    );
  };

  // 查看初始化设备配置
  const handleView = (record: any) => {
    navigate(`/configTemplate/deviceConfig/${record.templateNo}`);
  };

  // 查看初始化记录
  const handleConfigView = (record: any) => {
    navigate(`/configTemplate/initRecord/${record.templateNo}`);
  };

  // 编辑配置模板
  const handleEdit = (record: any) => {
    navigate(`/configTemplate/edit/${record.templateNo}`);
  };
  // 启用/禁用配置模板
  const handleEnable = async (record: any) => {
    try {
      const res = await configTemplateApi.current.enableConfigTemplate({
        templateNo: record.templateNo,
        enable: record.enable === 1 ? 0 : 1,
      });

      if (res.code === HttpStatusCode.Success) {
        message.success('操作成功');
        setTableKey(Date.now().toString()); // 刷新表格
      } else {
        message.error(res.message || '操作失败');
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 复制配置模板
  const handleCopy = (record: any) => {
    navigate(`/configTemplate/copy/${record.templateNo}`);
  };
  // 删除配置模板
  const handleDelete = async (record: any) => {
    try {
      const res = await configTemplateApi.current.deleteConfigTemplate({
        templateNo: record.templateNo,
      });

      if (res.code === HttpStatusCode.Success) {
        message.success('删除成功');
        setTableKey(Date.now().toString()); // 刷新表格
      } else {
        message.error(res.message || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  const formatColumns = () => {
    return TableConfig.map((col) => {
      switch (col.dataIndex) {
        case 'index':
          return {
            ...col,
            render: (_: any, __: any, index: number) => {
              return (
                (searchCondition.pageNum - 1) * searchCondition.pageSize +
                index +
                1
              );
            },
          };
        case 'enable':
          return {
            ...col,
            render: (enable: number) => (enable === 1 ? '启用' : '禁用'),
          };
        case 'operation':
          return {
            ...col,
            render: (_: any, record: any) => {
              return (
                <div className="operate">
                  <a onClick={() => handleView(record)}>初始化设备配置</a>
                  <a onClick={() => handleConfigView(record)}>初始化记录查询</a>
                  <a onClick={() => handleEdit(record)}>编辑</a>
                  <Popconfirm
                    title={`确认${
                      record.enable === 1 ? '禁用' : '启用'
                    }该配置模板？`}
                    onConfirm={() => handleEnable(record)}
                  >
                    <a>{record.enable === 1 ? '禁用' : '启用'}</a>
                  </Popconfirm>
                  <a onClick={() => handleCopy(record)}>复制</a>
                  <Popconfirm
                    title="确认删除该配置模板？"
                    onConfirm={() => handleDelete(record)}
                  >
                    <a>删除</a>
                  </Popconfirm>
                </div>
              );
            },
          };
        default:
          return {
            ...col,
            render: (text: any) => `${text || '-'}`,
          };
      }
    });
  };
  const handleAdd = () => {
    navigate(`/configTemplate/add?type=add`);
  };
  const middleBtns: any[] = [
    {
      show: true,
      title: '创建配置模板',
      key: 'add',
      onClick: () => handleAdd(),
    },
  ];
  return (
    <div className="config-template-manage">
      <div className="search-form">
        <CommonForm
          formConfig={SearchConfig}
          layout="inline"
          formType="search"
          colon={false}
          onSearchClick={onSearchClick}
          onResetClick={onResetClick}
          defaultValue={searchCondition.searchForm}
        />
      </div>
      <div className="table-container">
        <CommonTable
          loading={loading}
          columns={formatColumns()}
          middleBtns={middleBtns}
          searchCondition={searchCondition}
          tableListData={{
            list: tableData?.list || [],
            totalPage: tableData?.pages,
            totalNumber: tableData?.total,
          }}
          rowKey="templateNo"
          onPageChange={(pagination: any) => {
            const newSearchCondition = {
              ...searchCondition,
              pageNum: pagination.pageNum,
              pageSize: pagination.pageSize,
            };
            setSearchCondition(newSearchCondition);

            // 保存分页变化到 store
            dispatch(
              saveSearchValues({
                routeName: location.pathname,
                searchValues: newSearchCondition,
              }),
            );
          }}
        />
      </div>
    </div>
  );
};

export default ConfigTemplateManage;
