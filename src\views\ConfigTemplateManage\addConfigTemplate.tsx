import React, { useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import Configuration, { SelectedData } from './components/Configuration';
import './addConfigTemplate.scss';
import { ConfigTemplateApi } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';
import CommonSteps from '@/components/CommonSteps';
import BreadCrumb from '@/components/BreadCrumb';
import EditConfiguration from './components/EditConfiguration';

const AddConfigTemplate = () => {
  const configRef = useRef<{
    checkAndGetSelectedData: () => Promise<SelectedData>;
  }>();
  const editConfigRef = useRef<any>(null);
  const [selectedConfig, setSelectedConfig] = useState<SelectedData>({
    product: {},
    blockData: [],
  });

  const checkStepOne = async () => {
    try {
      const selectedData: any =
        await configRef.current?.checkAndGetSelectedData();
      console.log('选中的数据：', selectedData);
      setSelectedConfig(selectedData);
      return true;
    } catch (error: any) {
      message.error(error.message);
      return false;
    }
  };

  const handleSubmit = async () => {
    const inputValues = editConfigRef.current?.getInputValues();
    console.log('Input Values:', inputValues);
  };

  return (
    <>
      <BreadCrumb
        items={[
          { title: '通用设备管理', route: '' },
          { title: '配置模板管理', route: '/ota/configTemplate' },
          { title: '创建配置模板', route: '' },
        ]}
      />
      <div className="add-config-template-content">
        <CommonSteps
          stepTipList={['选择配置项', '编辑配置模版', '保存成功']}
          children={[
            <Configuration onRef={(ref) => (configRef.current = ref)} />,
            <EditConfiguration
              ref={editConfigRef}
              selectedConfig={selectedConfig}
            />,
          ]}
          onNextCheckList={[checkStepOne]}
          onSubmit={handleSubmit}
        />
      </div>
    </>
  );
};

export default AddConfigTemplate;
