import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
  useMemo,
} from 'react';
import './index.scss';
import { CommonTable, useTableData, CommonForm } from '@jd/x-coreui';
import CommandControlFetch from '@/fetch/bussiness/commandControl';
import { Device } from '@/fetch/bussiness';
import FileUpload from './FileUpload';
import { flat, isEmpty, NULL_GROUP, formatTreeData } from '@/utils/utils';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import {
  addGlobalEventListener,
  removeGlobalEventListener,
} from '@/utils/emit';
import { FormConfig } from '@jd/x-coreui/es/components/CommonForm';
import {
  DeviceChoiceTypeMap,
  StepEditFormConfig,
  DeviceTableColumns,
  DeviceSearchForm,
} from './utils/constant';
import { message } from 'antd';
import { HttpStatusCode } from '@/fetch/core/constant';
import { cloneDeep } from 'lodash';
import classNames from 'classnames';

const commonApi = new CommandControlFetch();
const deviceApi = new Device();

const DeviceSelection = forwardRef(
  (
    props: {
      dataFromPreviousStep?: any;
      deviceChoiceType?: number;
      extraEditFormConfig?: FormConfig;
      needCheckDeviceDetail?: boolean;
      fetchDeviceListApi?: any;
      updateSelectDevice: (selectKeys: any[], total?: number) => void;
      updateDeviceChoiceType: (value: number) => void;
      hideSearchFormConfig?: boolean;
    },
    ref: any,
  ) => {
    const {
      fetchDeviceListApi = commonApi.getDeviceList,
      dataFromPreviousStep,
      deviceChoiceType,
      needCheckDeviceDetail,
      extraEditFormConfig,
      updateSelectDevice,
      updateDeviceChoiceType,
      hideSearchFormConfig,
    } = props;
    const initSearchCondition = {
      productKey: null,
      productModelNoList: null,
      groupNoList: null,
      appType: null,
      appName: null,
      appVersionNumber: null,
      deviceName: null,
      pageNum: 1,
      pageSize: 10,
      hasSearched: false,
    };

    const createOTATask = useSelector(
      (state: RootState) => state.createOTATask,
    );
    const conditionRef = useRef<any>(null);
    const deviceChoiceTypeRef = useRef<any>(null);
    const [searchCondition, setSearchCondition] =
      useState<any>(initSearchCondition);
    const [uploadResult, setUploadResult] = useState<any>(null);
    const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
    const [autoFetch, setAutoFetch] = useState<boolean>(false);
    const [editFormConfig, setEditFormConfig] =
      useState<any>(StepEditFormConfig);
    const [dataFromEditForm, setDataFromEditForm] = useState<any>(null);

    const { tableData, loading, reloadTable } = useTableData(
      searchCondition,
      fetchDeviceListApi,
      'deviceSelection',
      autoFetch,
    );

    const validateKeyAndModel = () => {
      if (!dataFromPreviousStep && deviceChoiceTypeRef?.current) {
        const { productKey, productModelNoList } =
          deviceChoiceTypeRef.current.getFieldsValue();
        if (!productKey || isEmpty(productModelNoList)) {
          return false;
        }
      }
      return true;
    };

    const getKeyAndModel = () => {
      if (!dataFromPreviousStep && deviceChoiceTypeRef?.current) {
        const value = deviceChoiceTypeRef.current.getFieldsValue();
        return {
          productKey: value?.productKey,
          productModelNoList: value?.productModelNoList,
        };
      } else {
        return {
          productKey: dataFromPreviousStep?.productKey,
          productModelNoList: dataFromPreviousStep?.productModelNoList,
        };
      }
    };

    const setKeyAndModelInForm = (
      productKey: any,
      productModelNoList: any,
      syncEditInfo: boolean = false,
    ) => {
      conditionRef.current.setFieldsValue({
        productKey,
        productModelNoList,
      });
      console.log('condition ref', conditionRef.current.getFieldsValue());
      if (syncEditInfo) {
        deviceChoiceTypeRef.current.setFieldsValue({
          productKey,
          productModelNoList,
        });
      }
    };

    const onSearchClick = (values: any) => {
      if (!validateKeyAndModel()) {
        message.error('请先选择产品和型号');
        return;
      }

      const { groupNoList, ...otherData } = values || {};
      const groupList = new Set(flat(groupNoList) || []);
      const { productKey, productModelNoList } = getKeyAndModel();
      setSearchCondition({
        ...searchCondition,
        ...otherData,
        groupNoList: [...groupList],
        productKey,
        productModelNoList,
        hasSearched: true,
        pageNum: 1,
        pageSize: 10,
      });
      setSelectedRowKeys([]);
      if (deviceChoiceType === DeviceChoiceTypeMap.CONDITIONAL) {
        updateSelectDevice([], tableData?.total);
      } else {
        updateSelectDevice([]);
      }
    };

    const onResetClick = () => {
      const { productKey, productModelNoList } = getKeyAndModel();
      const resetValue: any = {
        ...initSearchCondition,
        productKey,
        productModelNoList,
      };
      setSearchCondition(resetValue);
      setSelectedRowKeys([]);
      updateSelectDevice([]);
      conditionRef.current.setFieldsValue(resetValue);
    };

    // 表格列
    // 不同的：多了一个操作按钮
    // 相同的：序号列以及其他列字段
    const formattedColumns = useMemo(() => {
      let newDeviceTableColumns = DeviceTableColumns;
      if (!needCheckDeviceDetail) {
        newDeviceTableColumns = DeviceTableColumns.filter(
          (col: any) => col.dataIndex !== 'operate',
        );
      }
      return newDeviceTableColumns.map((col: any) => {
        switch (col.dataIndex) {
          case 'order':
            col.render = (text: any, record: any, index: number) =>
              `${
                (searchCondition.pageNum - 1) * searchCondition.pageSize +
                index +
                1
              }`;
            break;
          case 'operate':
            col.render = (text: any, record: any) => {
              return (
                <div className="operate-btn">
                  <a>查看配置</a>
                </div>
              );
            };
            break;
          default:
            break;
        }
        return col;
      });
    }, [searchCondition.pageNum, searchCondition.pageSize]);

    const enhancedSearchFormConfig = useMemo(() => {
      if (!hideSearchFormConfig) {
        return DeviceSearchForm;
      }
      const clonedConfig = cloneDeep(DeviceSearchForm);
      const linkRules = clonedConfig.linkRules;
      if (linkRules?.productKey) {
        const { fetchProductKey, productKey, ...otherLinkRules } = linkRules;
        const filteredProductKeyRules =
          productKey?.filter(
            (item: any) => item.linkFieldName !== 'productModelNoList',
          ) || [];
        clonedConfig.linkRules = {
          ...otherLinkRules,
          ...(filteredProductKeyRules.length > 0 && {
            productKey: filteredProductKeyRules,
          }),
        };
      }
      return clonedConfig;
    }, [hideSearchFormConfig]);

    const onEditFormValueChange = (allValues: any, changedFieldName: any) => {
      const { productKey, productModelNoList, deviceChoiceType } = allValues;
      if (changedFieldName === 'deviceChoiceType') {
        updateDeviceChoiceType(deviceChoiceType);
      }
      if (
        productKey !== dataFromEditForm?.productKey ||
        productModelNoList?.toString() !==
          dataFromEditForm?.productModelNoList?.toString()
      ) {
        setKeyAndModelInForm(productKey, productModelNoList);
        setDataFromEditForm({
          productKey,
          productModelNoList,
        });
      }
    };

    const getSearchFormDefaultValue = () => {
      if (dataFromPreviousStep) {
        return {
          productKey: createOTATask.info
            ? createOTATask.info.productKey
            : dataFromPreviousStep?.productKey,
          productModelNoList: createOTATask.info
            ? createOTATask.info.productModelNoList
            : dataFromPreviousStep?.productModelNoList,
          fetchProductKey: true,
        };
      }
      return {
        productKey: createOTATask.info
          ? createOTATask.info.productKey
          : dataFromEditForm?.productKey,
      };
    };

    // 给外部获取上传结果
    // 全部相同的
    const getUploadResult = () => {
      return uploadResult;
    };

    // 拿到表单的字段值
    // 全部相同
    const getConditionFormValues = () => {
      return {
        ...searchCondition,
      };
    };

    // 给外部的ref提供的方法
    // 全部相同
    useImperativeHandle(ref, () => {
      return {
        getUploadResult,
        getConditionFormValues,
      };
    });
    // 选中列表，考虑换成x-coreui里面的，props.updateSelectDevice(newSelectedRowKeys)拉出来
    // 全部相同
    const rowSelection = {
      selectedRowKeys: selectedRowKeys,
      onSelect: (record: any, selected: boolean) => {
        if (selected) {
          const newSelectedRowKeys = [...selectedRowKeys, record.deviceName];
          setSelectedRowKeys(newSelectedRowKeys);
          updateSelectDevice(newSelectedRowKeys);
        } else {
          const newSelectedRowKeys = selectedRowKeys.filter(
            (v: string) => v !== record.deviceName,
          );
          setSelectedRowKeys(newSelectedRowKeys);
          updateSelectDevice(newSelectedRowKeys);
        }
      },
      onSelectAll: (selected: boolean, selectedRows: any, changeRows: any) => {
        if (selected) {
          const set1 = new Set(selectedRowKeys);
          changeRows.forEach((v: any) => {
            set1.add(v.deviceName);
          });
          setSelectedRowKeys([...set1]);
          updateSelectDevice([...set1]);
        } else {
          const arr2 = selectedRowKeys.filter((v: any) => {
            return !changeRows.some((i: any) => i.deviceName === v);
          });
          setSelectedRowKeys([...arr2]);
          updateSelectDevice([...arr2]);
        }
      },
    };

    // 共同有的，等到key和model的数变了，才加载列表，不要一进入页面就加载
    useEffect(() => {
      if (deviceChoiceType === DeviceChoiceTypeMap.UPLOAD) {
        return;
      }
      const { productKey, productModelNoList } = getKeyAndModel();
      if (productKey && !isEmpty(productModelNoList)) {
        if (dataFromPreviousStep) {
          conditionRef.current.setFieldsValue({
            productKey,
            productModelNoList,
          });
        }
        setSearchCondition({
          ...searchCondition,
          productKey,
          productModelNoList,
        });
        setAutoFetch(true);
      }
    }, [
      dataFromPreviousStep?.productKey,
      dataFromPreviousStep?.productModelNoList?.toString(),
      dataFromEditForm?.productKey,
      dataFromEditForm?.productModelNoList?.toString(),
      deviceChoiceType,
    ]);

    // 共同有的，表单存deviceChoiceType，定向更新外部
    useEffect(() => {
      deviceChoiceTypeRef.current.setFieldValue(
        'deviceChoiceType',
        deviceChoiceType,
      );

      if (deviceChoiceType === DeviceChoiceTypeMap.DIRECTIONAL) {
        updateSelectDevice(selectedRowKeys);
      }
    }, [deviceChoiceType]);

    // 共同有的，条件搜索传外部勾选
    useEffect(() => {
      if (
        deviceChoiceType === DeviceChoiceTypeMap.CONDITIONAL &&
        searchCondition.hasSearched
      ) {
        updateSelectDevice([], tableData?.total);
      }
    }, [tableData?.total]);

    // 共同，历史数据，退出列表页面再进来
    useEffect(() => {
      if (createOTATask.info) {
        const { productKey, productModelNoList, deviceNameList } =
          createOTATask.info;
        const searchValue = {
          ...searchCondition,
          productKey,
          productModelNoList,
        };
        setKeyAndModelInForm(productKey, productModelNoList, true);
        setSearchCondition(searchValue);
        if (!isEmpty(deviceNameList)) {
          setSelectedRowKeys(deviceNameList);
          updateSelectDevice(deviceNameList);
        }
      }
    }, [createOTATask.info]);

    // 发布计划才有的，依赖于上一步的结果
    useEffect(() => {
      if (!dataFromPreviousStep) {
        return;
      }
      const cb = () => {
        setSelectedRowKeys([]);
        updateSelectDevice([]);
        updateDeviceChoiceType(0);
        const initValue = {
          ...initSearchCondition,
          productKey: dataFromPreviousStep?.productKey,
          productModelNoList: dataFromPreviousStep?.productModelNoList,
        };
        setSearchCondition(initValue);
        conditionRef.current?.setFieldsValue(initValue);
      };
      addGlobalEventListener('PRODUCT_HAS_CHANGED', cb);
      return () => {
        removeGlobalEventListener('PRODUCT_HAS_CHANGED', cb);
      };
    }, [
      dataFromPreviousStep?.productKey,
      dataFromPreviousStep?.productModelNoList?.toString(),
    ]);

    // 配置才有的，进来拉产品的下拉选项接口
    useEffect(() => {
      if (dataFromPreviousStep || !extraEditFormConfig) {
        return;
      }
      deviceApi.queryProductList().then((res) => {
        if (res?.code === HttpStatusCode.Success) {
          const productKeyField = extraEditFormConfig?.fields.find(
            (field: any) => field.fieldName === 'productKey',
          );
          productKeyField!.options = res.data.map((item: any) => ({
            label: item.productName,
            value: item.productKey,
          }));
          setEditFormConfig({
            fields: [
              ...((extraEditFormConfig?.fields as any[]) || []),
              ...StepEditFormConfig?.fields,
            ],
            linkRules: {
              ...(extraEditFormConfig?.linkRules || {}),
            },
          });
        }
      });
    }, []);

    const renderChildCmp = () => {
      switch (deviceChoiceType) {
        case 0:
        case 2:
          return (
            <div
              className={classNames({
                'hide-search-form': hideSearchFormConfig,
              })}
              style={{ position: 'relative' }}
            >
              <CommonForm
                defaultValue={getSearchFormDefaultValue()}
                formConfig={enhancedSearchFormConfig}
                formType="search"
                layout="inline"
                getFormInstance={(ref: any) => {
                  conditionRef.current = ref;
                }}
                onSearchClick={onSearchClick}
                onResetClick={onResetClick}
              />

              <CommonTable
                searchCondition={searchCondition}
                columns={formattedColumns}
                tableListData={{
                  list: tableData?.list || [],
                  totalPage: tableData?.pages,
                  totalNumber: tableData?.total,
                }}
                onPageChange={(paginationData: any) => {
                  const val = {
                    ...searchCondition,
                    pageNum: paginationData.pageNum,
                    pageSize: paginationData.pageSize,
                  };
                  setSearchCondition(val);
                }}
                rowKey={'deviceName'}
                rowSelection={deviceChoiceType != 2 ? rowSelection : null}
              />
              <span
                className="select-num"
                style={{
                  position: 'absolute',
                  fontSize: '14px',
                  left: '20px',
                  bottom: '36px',
                  fontFamily: 'PingFang SC',
                  fontWeight: 'normal',
                  color: 'rgb(153, 153, 153)',
                }}
              >
                已选择
                {deviceChoiceType != 2
                  ? selectedRowKeys.length
                  : searchCondition.hasSearched
                  ? tableData?.total
                  : 0}
                ，共{tableData?.total}
              </span>
            </div>
          );
        case 1:
          return (
            <FileUpload
              productKey={
                dataFromPreviousStep
                  ? dataFromPreviousStep?.productKey
                  : dataFromEditForm?.productKey
              }
              modelList={
                dataFromPreviousStep
                  ? dataFromPreviousStep?.productModelNoList
                  : dataFromEditForm?.productModelNoList
              }
              uploadChange={setUploadResult}
            />
          );
        default:
          break;
      }
    };

    return (
      <div className="device-selection">
        <CommonForm
          defaultValue={{ deviceChoiceType }}
          getFormInstance={(ref: any) => {
            deviceChoiceTypeRef.current = ref;
          }}
          layout="inline"
          formConfig={editFormConfig}
          onValueChange={onEditFormValueChange}
        />
        {renderChildCmp()}
      </div>
    );
  },
);

export default DeviceSelection;
