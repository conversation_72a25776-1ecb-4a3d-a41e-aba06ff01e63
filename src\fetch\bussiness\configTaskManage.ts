import { request } from '@/fetch/core';

// 基于示例数据结构生成的21条mock数据
const mockDataList = [
  {
    code: '0000',
    message: '成功',
    data: {
      taskNo: 'TASK001',
      blockName: '配置块A',
      blockNo: 'BLK001',
      taskStatus: 1,
      taskStatusName: '已生效',
      effectiveTime: '2025-01-01 08:00:00',
      immediately: 1,
      immediatelyName: '立即推送',
      executionTime: '2025-01-01 08:00:00',
      batch: 1,
      batchName: '分批',
      batchCount: 10,
      batchInterval: 60,
      createUser: '张三',
      createTime: '2024-12-25 08:00:00',
    },
    traceId: '4438529.66111.17388197119381835',
  },
  {
    code: '0000',
    message: '成功',
    data: {
      taskNo: 'TASK002',
      blockName: '配置块B',
      blockNo: 'BLK002',
      taskStatus: 2,
      taskStatusName: '执行中',
      effectiveTime: '2025-01-02 09:30:00',
      immediately: 0,
      immediatelyName: '定时推送',
      executionTime: '2025-01-02 09:30:00',
      batch: 1,
      batchName: '分批',
      batchCount: 15,
      batchInterval: 120,
      createUser: '李四',
      createTime: '2024-12-26 09:30:00',
    },
    traceId: '4438529.66111.17388197119381836',
  },
  {
    code: '0000',
    message: '成功',
    data: {
      taskNo: 'TASK003',
      blockName: '配置块C',
      blockNo: 'BLK003',
      taskStatus: 3,
      taskStatusName: '已完成',
      effectiveTime: '2025-01-03 10:15:00',
      immediately: 1,
      immediatelyName: '立即推送',
      executionTime: '2025-01-03 10:15:00',
      batch: 0,
      batchName: '全量',
      batchCount: 0,
      batchInterval: 0,
      createUser: '王五',
      createTime: '2024-12-27 10:15:00',
    },
    traceId: '4438529.66111.17388197119381837',
  },
  {
    code: '0000',
    message: '成功',
    data: {
      taskNo: 'TASK004',
      blockName: '配置块D',
      blockNo: 'BLK004',
      taskStatus: 0,
      taskStatusName: '待执行',
      effectiveTime: '2025-01-04 14:20:00',
      immediately: 0,
      immediatelyName: '定时推送',
      executionTime: '2025-01-04 14:20:00',
      batch: 1,
      batchName: '分批',
      batchCount: 20,
      batchInterval: 180,
      createUser: '赵六',
      createTime: '2024-12-28 14:20:00',
    },
    traceId: '4438529.66111.17388197119381838',
  },
  {
    code: '0000',
    message: '成功',
    data: {
      taskNo: 'TASK005',
      blockName: '配置块E',
      blockNo: 'BLK005',
      taskStatus: 4,
      taskStatusName: '执行失败',
      effectiveTime: '2025-01-05 16:45:00',
      immediately: 1,
      immediatelyName: '立即推送',
      executionTime: '2025-01-05 16:45:00',
      batch: 1,
      batchName: '分批',
      batchCount: 5,
      batchInterval: 30,
      createUser: '陈七',
      createTime: '2024-12-29 16:45:00',
    },
    traceId: '4438529.66111.17388197119381839',
  },
  {
    code: '0000',
    message: '成功',
    data: {
      taskNo: 'TASK006',
      blockName: '配置块F',
      blockNo: 'BLK006',
      taskStatus: 1,
      taskStatusName: '已生效',
      effectiveTime: '2025-01-06 11:30:00',
      immediately: 0,
      immediatelyName: '定时推送',
      executionTime: '2025-01-06 11:30:00',
      batch: 0,
      batchName: '全量',
      batchCount: 0,
      batchInterval: 0,
      createUser: '孙八',
      createTime: '2024-12-30 11:30:00',
    },
    traceId: '4438529.66111.17388197119381840',
  },
  {
    code: '0000',
    message: '成功',
    data: {
      taskNo: 'TASK007',
      blockName: '配置块G',
      blockNo: 'BLK007',
      taskStatus: 2,
      taskStatusName: '执行中',
      effectiveTime: '2025-01-07 13:15:00',
      immediately: 1,
      immediatelyName: '立即推送',
      executionTime: '2025-01-07 13:15:00',
      batch: 1,
      batchName: '分批',
      batchCount: 25,
      batchInterval: 240,
      createUser: '周九',
      createTime: '2024-12-31 13:15:00',
    },
    traceId: '4438529.66111.17388197119381841',
  },
  {
    code: '0000',
    message: '成功',
    data: {
      taskNo: 'TASK008',
      blockName: '配置块H',
      blockNo: 'BLK008',
      taskStatus: 3,
      taskStatusName: '已完成',
      effectiveTime: '2025-01-08 15:00:00',
      immediately: 0,
      immediatelyName: '定时推送',
      executionTime: '2025-01-08 15:00:00',
      batch: 1,
      batchName: '分批',
      batchCount: 8,
      batchInterval: 90,
      createUser: '吴十',
      createTime: '2025-01-01 15:00:00',
    },
    traceId: '4438529.66111.17388197119381842',
  },
  {
    code: '0000',
    message: '成功',
    data: {
      taskNo: 'TASK009',
      blockName: '配置块I',
      blockNo: 'BLK009',
      taskStatus: 0,
      taskStatusName: '待执行',
      effectiveTime: '2025-01-09 09:45:00',
      immediately: 1,
      immediatelyName: '立即推送',
      executionTime: '2025-01-09 09:45:00',
      batch: 0,
      batchName: '全量',
      batchCount: 0,
      batchInterval: 0,
      createUser: '郑十一',
      createTime: '2025-01-02 09:45:00',
    },
    traceId: '4438529.66111.17388197119381843',
  },
  {
    code: '0000',
    message: '成功',
    data: {
      taskNo: 'TASK010',
      blockName: '配置块J',
      blockNo: 'BLK010',
      taskStatus: 4,
      taskStatusName: '执行失败',
      effectiveTime: '2025-01-10 12:20:00',
      immediately: 0,
      immediatelyName: '定时推送',
      executionTime: '2025-01-10 12:20:00',
      batch: 1,
      batchName: '分批',
      batchCount: 12,
      batchInterval: 150,
      createUser: '王十二',
      createTime: '2025-01-03 12:20:00',
    },
    traceId: '4438529.66111.17388197119381844',
  },
  {
    code: '0000',
    message: '成功',
    data: {
      taskNo: 'TASK011',
      blockName: '配置块K',
      blockNo: 'BLK011',
      taskStatus: 1,
      taskStatusName: '已生效',
      effectiveTime: '2025-01-11 17:30:00',
      immediately: 1,
      immediatelyName: '立即推送',
      executionTime: '2025-01-11 17:30:00',
      batch: 1,
      batchName: '分批',
      batchCount: 18,
      batchInterval: 200,
      createUser: '冯十三',
      createTime: '2025-01-04 17:30:00',
    },
    traceId: '4438529.66111.17388197119381845',
  },
  {
    code: '0000',
    message: '成功',
    data: {
      taskNo: 'TASK012',
      blockName: '配置块L',
      blockNo: 'BLK012',
      taskStatus: 2,
      taskStatusName: '执行中',
      effectiveTime: '2025-01-12 08:15:00',
      immediately: 0,
      immediatelyName: '定时推送',
      executionTime: '2025-01-12 08:15:00',
      batch: 1,
      batchName: '分批',
      batchCount: 30,
      batchInterval: 300,
      createUser: '陈十四',
      createTime: '2025-01-05 08:15:00',
    },
    traceId: '4438529.66111.17388197119381846',
  },
  {
    code: '0000',
    message: '成功',
    data: {
      taskNo: 'TASK013',
      blockName: '配置块M',
      blockNo: 'BLK013',
      taskStatus: 3,
      taskStatusName: '已完成',
      effectiveTime: '2025-01-13 19:00:00',
      immediately: 1,
      immediatelyName: '立即推送',
      executionTime: '2025-01-13 19:00:00',
      batch: 0,
      batchName: '全量',
      batchCount: 0,
      batchInterval: 0,
      createUser: '卫十五',
      createTime: '2025-01-06 19:00:00',
    },
    traceId: '4438529.66111.17388197119381847',
  },
  {
    code: '0000',
    message: '成功',
    data: {
      taskNo: 'TASK014',
      blockName: '配置块N',
      blockNo: 'BLK014',
      taskStatus: 0,
      taskStatusName: '待执行',
      effectiveTime: '2025-01-14 10:30:00',
      immediately: 0,
      immediatelyName: '定时推送',
      executionTime: '2025-01-14 10:30:00',
      batch: 1,
      batchName: '分批',
      batchCount: 6,
      batchInterval: 45,
      createUser: '蒋十六',
      createTime: '2025-01-07 10:30:00',
    },
    traceId: '4438529.66111.17388197119381848',
  },
  {
    code: '0000',
    message: '成功',
    data: {
      taskNo: 'TASK015',
      blockName: '配置块O',
      blockNo: 'BLK015',
      taskStatus: 4,
      taskStatusName: '执行失败',
      effectiveTime: '2025-01-15 14:45:00',
      immediately: 1,
      immediatelyName: '立即推送',
      executionTime: '2025-01-15 14:45:00',
      batch: 1,
      batchName: '分批',
      batchCount: 22,
      batchInterval: 180,
      createUser: '沈十七',
      createTime: '2025-01-08 14:45:00',
    },
    traceId: '4438529.66111.17388197119381849',
  },
  {
    code: '0000',
    message: '成功',
    data: {
      taskNo: 'TASK016',
      blockName: '配置块P',
      blockNo: 'BLK016',
      taskStatus: 1,
      taskStatusName: '已生效',
      effectiveTime: '2025-01-16 16:20:00',
      immediately: 0,
      immediatelyName: '定时推送',
      executionTime: '2025-01-16 16:20:00',
      batch: 0,
      batchName: '全量',
      batchCount: 0,
      batchInterval: 0,
      createUser: '韩十八',
      createTime: '2025-01-09 16:20:00',
    },
    traceId: '4438529.66111.17388197119381850',
  },
  {
    code: '0000',
    message: '成功',
    data: {
      taskNo: 'TASK017',
      blockName: '配置块Q',
      blockNo: 'BLK017',
      taskStatus: 2,
      taskStatusName: '执行中',
      effectiveTime: '2025-01-17 11:10:00',
      immediately: 1,
      immediatelyName: '立即推送',
      executionTime: '2025-01-17 11:10:00',
      batch: 1,
      batchName: '分批',
      batchCount: 14,
      batchInterval: 120,
      createUser: '杨十九',
      createTime: '2025-01-10 11:10:00',
    },
    traceId: '4438529.66111.17388197119381851',
  },
  {
    code: '0000',
    message: '成功',
    data: {
      taskNo: 'TASK018',
      blockName: '配置块R',
      blockNo: 'BLK018',
      taskStatus: 3,
      taskStatusName: '已完成',
      effectiveTime: '2025-01-18 13:35:00',
      immediately: 0,
      immediatelyName: '定时推送',
      executionTime: '2025-01-18 13:35:00',
      batch: 1,
      batchName: '分批',
      batchCount: 16,
      batchInterval: 210,
      createUser: '朱二十',
      createTime: '2025-01-11 13:35:00',
    },
    traceId: '4438529.66111.17388197119381852',
  },
  {
    code: '0000',
    message: '成功',
    data: {
      taskNo: 'TASK019',
      blockName: '配置块S',
      blockNo: 'BLK019',
      taskStatus: 0,
      taskStatusName: '待执行',
      effectiveTime: '2025-01-19 15:50:00',
      immediately: 1,
      immediatelyName: '立即推送',
      executionTime: '2025-01-19 15:50:00',
      batch: 0,
      batchName: '全量',
      batchCount: 0,
      batchInterval: 0,
      createUser: '秦二十一',
      createTime: '2025-01-12 15:50:00',
    },
    traceId: '4438529.66111.17388197119381853',
  },
  {
    code: '0000',
    message: '成功',
    data: {
      taskNo: 'TASK020',
      blockName: '配置块T',
      blockNo: 'BLK020',
      taskStatus: 4,
      taskStatusName: '执行失败',
      effectiveTime: '2025-01-20 09:25:00',
      immediately: 0,
      immediatelyName: '定时推送',
      executionTime: '2025-01-20 09:25:00',
      batch: 1,
      batchName: '分批',
      batchCount: 28,
      batchInterval: 360,
      createUser: '尤二十二',
      createTime: '2025-01-13 09:25:00',
    },
    traceId: '4438529.66111.17388197119381854',
  },
  {
    code: '0000',
    message: '成功',
    data: {
      taskNo: 'TASK021',
      blockName: '配置块U',
      blockNo: 'BLK021',
      taskStatus: 1,
      taskStatusName: '已生效',
      effectiveTime: '2025-01-21 18:40:00',
      immediately: 1,
      immediatelyName: '立即推送',
      executionTime: '2025-01-21 18:40:00',
      batch: 1,
      batchName: '分批',
      batchCount: 35,
      batchInterval: 480,
      createUser: '许二十三',
      createTime: '2025-01-14 18:40:00',
    },
    traceId: '4438529.66111.17388197119381855',
  },
];

const listData = {
  pageNum: 1,
  pageSize: 21,
  pages: 1,
  total: 21,
  list: [
    {
      taskNo: 'TASK001',
      productModelNames: 'ModelB,ModelD',
      blockNames: 'Block2',
      taskStatus: 2,
      taskStatusName: '已生效',
      successTotal: 12,
      failureTotal: 3,
      immediately: 1,
      issueTime: '2024-07-10 09:15:00',
      deviceTotal: 15,
      createUser: '张三',
      createTime: '2024-07-10 09:15:00',
    },
    {
      taskNo: 'TASK002',
      productModelNames: 'ModelA',
      blockNames: 'Block1,Block4',
      taskStatus: 1,
      taskStatusName: '待生效',
      successTotal: 0,
      failureTotal: 8,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-03-05 14:00:00',
      deviceTotal: 8,
      createUser: '李四',
      createTime: '2024-02-20 14:00:00',
    },
    {
      taskNo: 'TASK003',
      productModelNames: 'ModelC,ModelE,ModelA',
      blockNames: 'Block3',
      taskStatus: 3,
      taskStatusName: '已取消',
      successTotal: 5,
      failureTotal: 5,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-05-25 10:30:00',
      deviceTotal: 10,
      createUser: '王五',
      createTime: '2024-05-15 10:30:00',
    },
    {
      taskNo: 'TASK004',
      productModelNames: 'ModelD',
      blockNames: 'Block5',
      taskStatus: 4,
      taskStatusName: '创建失败',
      successTotal: 0,
      failureTotal: 0,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-11-01 08:00:00',
      deviceTotal: 0,
      createUser: '赵六',
      createTime: '2024-11-01 08:00:00',
    },
    {
      taskNo: 'TASK005',
      productModelNames: 'ModelE',
      blockNames: 'Block1',
      taskStatus: 0,
      taskStatusName: '创建中',
      successTotal: 0,
      failureTotal: 0,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-04-02 16:45:00',
      deviceTotal: 0,
      createUser: '陈七',
      createTime: '2024-03-28 16:45:00',
    },
    {
      taskNo: 'TASK006',
      productModelNames: 'ModelA,ModelC',
      blockNames: 'Block2,Block3',
      taskStatus: 2,
      taskStatusName: '已生效',
      successTotal: 7,
      failureTotal: 3,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-08-12 11:20:00',
      deviceTotal: 10,
      createUser: '张三',
      createTime: '2024-08-12 11:20:00',
    },
    {
      taskNo: 'TASK007',
      productModelNames: 'ModelB',
      blockNames: 'Block4',
      taskStatus: 1,
      taskStatusName: '待生效',
      successTotal: 10,
      failureTotal: 5,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-09-01 00:00:00',
      deviceTotal: 15,
      createUser: '李四',
      createTime: '2024-08-25 00:00:00',
    },
    {
      taskNo: 'TASK008',
      productModelNames: 'ModelD,ModelE',
      blockNames: 'Block5',
      taskStatus: 2,
      taskStatusName: '已生效',
      successTotal: 18,
      failureTotal: 2,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-06-15 14:10:00',
      deviceTotal: 20,
      createUser: '王五',
      createTime: '2024-06-15 14:10:00',
    },
    {
      taskNo: 'TASK009',
      productModelNames: 'ModelC',
      blockNames: 'Block1',
      taskStatus: 3,
      taskStatusName: '已取消',
      successTotal: 0,
      failureTotal: 8,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-10-10 09:00:00',
      deviceTotal: 8,
      createUser: '赵六',
      createTime: '2024-10-01 09:00:00',
    },
    {
      taskNo: 'TASK010',
      productModelNames: 'ModelA,ModelB,ModelC',
      blockNames: 'Block2',
      taskStatus: 4,
      taskStatusName: '创建失败',
      successTotal: 0,
      failureTotal: 0,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-12-05 17:30:00',
      deviceTotal: 0,
      createUser: '陈七',
      createTime: '2024-12-05 17:30:00',
    },
    {
      taskNo: 'TASK011',
      productModelNames: 'ModelE',
      blockNames: 'Block3,Block4',
      taskStatus: 2,
      taskStatusName: '已生效',
      successTotal: 9,
      failureTotal: 1,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-04-20 13:45:00',
      deviceTotal: 10,
      createUser: '张三',
      createTime: '2024-04-20 13:45:00',
    },
    {
      taskNo: 'TASK012',
      productModelNames: 'ModelD',
      blockNames: 'Block5',
      taskStatus: 1,
      taskStatusName: '待生效',
      successTotal: 6,
      failureTotal: 4,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-07-22 08:00:00',
      deviceTotal: 10,
      createUser: '李四',
      createTime: '2024-07-15 08:00:00',
    },
    {
      taskNo: 'TASK013',
      productModelNames: 'ModelA,ModelE',
      blockNames: 'Block1',
      taskStatus: 0,
      taskStatusName: '创建中',
      successTotal: 0,
      failureTotal: 0,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-05-01 12:00:00',
      deviceTotal: 0,
      createUser: '王五',
      createTime: '2024-04-25 12:00:00',
    },
    {
      taskNo: 'TASK014',
      productModelNames: 'ModelB,ModelC',
      blockNames: 'Block2',
      taskStatus: 2,
      taskStatusName: '已生效',
      successTotal: 15,
      failureTotal: 5,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-09-10 10:10:10',
      deviceTotal: 20,
      createUser: '赵六',
      createTime: '2024-09-10 10:10:10',
    },
    {
      taskNo: 'TASK015',
      productModelNames: 'ModelD',
      blockNames: 'Block3',
      taskStatus: 3,
      taskStatusName: '已取消',
      successTotal: 0,
      failureTotal: 7,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-11-15 15:30:00',
      deviceTotal: 7,
      createUser: '陈七',
      createTime: '2024-11-10 15:30:00',
    },
    {
      taskNo: 'TASK016',
      productModelNames: 'ModelA,ModelB,ModelD',
      blockNames: 'Block4',
      taskStatus: 4,
      taskStatusName: '创建失败',
      successTotal: 0,
      failureTotal: 0,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-02-14 09:45:00',
      deviceTotal: 0,
      createUser: '张三',
      createTime: '2024-02-14 09:45:00',
    },
    {
      taskNo: 'TASK017',
      productModelNames: 'ModelC',
      blockNames: 'Block5',
      taskStatus: 2,
      taskStatusName: '已生效',
      successTotal: 8,
      failureTotal: 2,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-06-01 18:00:00',
      deviceTotal: 10,
      createUser: '李四',
      createTime: '2024-06-01 18:00:00',
    },
    {
      taskNo: 'TASK018',
      productModelNames: 'ModelE',
      blockNames: 'Block1,Block2',
      taskStatus: 1,
      taskStatusName: '待生效',
      successTotal: 4,
      failureTotal: 1,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-08-08 08:08:08',
      deviceTotal: 5,
      createUser: '王五',
      createTime: '2024-08-01 08:08:08',
    },
    {
      taskNo: 'TASK019',
      productModelNames: 'ModelA',
      blockNames: 'Block3',
      taskStatus: 0,
      taskStatusName: '创建中',
      successTotal: 0,
      failureTotal: 0,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-12-25 20:00:00',
      deviceTotal: 0,
      createUser: '赵六',
      createTime: '2024-12-20 20:00:00',
    },
    {
      taskNo: 'TASK020',
      productModelNames: 'ModelB,ModelE',
      blockNames: 'Block4',
      taskStatus: 2,
      taskStatusName: '已生效',
      successTotal: 10,
      failureTotal: 0,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-10-10 10:10:10',
      deviceTotal: 10,
      createUser: '陈七',
      createTime: '2024-10-10 10:10:10',
    },
    {
      taskNo: 'TASK021',
      productModelNames: 'ModelC,ModelD',
      blockNames: 'Block5',
      taskStatus: 3,
      taskStatusName: '已取消',
      successTotal: 0,
      failureTotal: 6,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-04-18 14:20:00',
      deviceTotal: 6,
      createUser: '张三',
      createTime: '2024-04-10 14:20:00',
    },
  ],
};

const getDeviceConfigDevicePage_data = {
  pageNum: 1,
  pageSize: 10,
  pages: 1,
  total: 5,
  list: [
    {
      groupLevelName: '分组/分组1',
      deviceName: '34899038',
      issueTime: '2024-12-11 12:12:30',
      issueDeviceStatus: 1,
      online: 1,
      onlineName: '在线',
      remark: '备注',
      isStop: 0,
      isStopName: '否',
      modifyTime: '2024-12-11 12:12:30',
    },
    {
      groupLevelName: '分组/分组2',
      deviceName: '34899039',
      issueTime: '2024-12-10 14:25:45',
      issueDeviceStatus: 2,
      online: 0,
      onlineName: '离线',
      remark: '设备维护中',
      isStop: 1,
      isStopName: '是',
      modifyTime: '2024-12-10 14:25:45',
    },
    {
      groupLevelName: '分组/分组1/子分组A',
      deviceName: '34899040',
      issueTime: '2024-12-09 09:18:22',
      issueDeviceStatus: 0,
      online: 1,
      onlineName: '在线',
      remark: '新添加设备',
      isStop: 0,
      isStopName: '否',
      modifyTime: '2024-12-09 09:18:22',
    },
    {
      groupLevelName: '分组/分组3',
      deviceName: '34899041',
      issueTime: '2024-12-08 16:42:10',
      issueDeviceStatus: 1,
      online: 1,
      onlineName: '在线',
      remark: '测试环境设备',
      isStop: 0,
      isStopName: '否',
      modifyTime: '2024-12-08 16:42:10',
    },
    {
      groupLevelName: '分组/分组2/子分组B',
      deviceName: '34899042',
      issueTime: '2024-12-07 11:05:33',
      issueDeviceStatus: 3,
      online: 0,
      onlineName: '离线',
      remark: '待修复',
      isStop: 1,
      isStopName: '是',
      modifyTime: '2024-12-07 11:05:33',
    },
  ],
};

class ConfigTaskManageApi {
  public getDeviceConfigIssueTaskPage = (params: {
    pageNum: number;
    pageSize: number;
    blockNo: string;
    taskStatus: number;
    taskNo: string;
    createTimeStart: string;
    createTimeEnd: string;
    createUser: string;
    productKey: string;
    productModelNo: string;
  }): Promise<any> => {
    const requestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_issue_task/get_device_config_issue_task_page',
      body: params,
      newGeteway: true,
    };
    // return request(requestOptions);
    return Promise.resolve({
      code: '0000',
      data: listData,
      message: 'ok',
    });
  };
  public getIssueDevicePageList = (params: {}) => {
    const requestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/issue/get_issue_device_page_list',
      body: params,
      newGeteway: true,
    };
    // return request(requestOptions);
    return Promise.resolve({
      code: '0000',
      data: getDeviceConfigDevicePage_data,
      message: 'ok',
    });
  };
  public createDeviceConfigIssueTask = (params: any) => {
    const requestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_issue_task/create_device_config_issue_task',
      body: params,
      newGeteway: true,
    };
    // return request(requestOptions);
    return Promise.resolve({
      code: '0000',
      data: false,
      message: 'ok',
    });
  };
  public getDeviceConfigIssueTask = (params: any) => {
    const requestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_template/get_device_config_issue_task',
      body: params,
      newGeteway: true,
    };
    // return request(requestOptions);
    return Promise.resolve({
      code: '0000',
      data: false,
      message: 'ok',
    });
  };
}

export default ConfigTaskManageApi;
