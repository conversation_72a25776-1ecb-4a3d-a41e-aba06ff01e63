import { request } from '@/fetch/core';

// 基于示例数据结构生成的21条mock数据
const mockDataList = {
  taskNo: 'TASK001',
  blockName: '配置块A',
  blockNo: 'BLK001',
  taskStatus: 1,
  taskStatusName: '已生效',
  effectiveTime: '2025-01-01 08:00:00',
  immediately: 1,
  immediatelyName: '立即推送',
  executionTime: '2025-01-01 08:00:00',
  batch: 1,
  batchName: '分批',
  batchCount: 10,
  batchInterval: 60,
  createUser: '张三',
  createTime: '2024-12-25 08:00:00',
};

const listData = {
  pageNum: 1,
  pageSize: 21,
  pages: 1,
  total: 21,
  list: [
    {
      taskNo: 'TASK001',
      productModelNames: 'ModelB,ModelD',
      blockNames: 'Block2',
      taskStatus: 2,
      taskStatusName: '已生效',
      successTotal: 12,
      failureTotal: 3,
      immediately: 1,
      issueTime: '2024-07-10 09:15:00',
      deviceTotal: 15,
      createUser: '张三',
      createTime: '2024-07-10 09:15:00',
    },
    {
      taskNo: 'TASK002',
      productModelNames: 'ModelA',
      blockNames: 'Block1,Block4',
      taskStatus: 1,
      taskStatusName: '待生效',
      successTotal: 0,
      failureTotal: 8,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-03-05 14:00:00',
      deviceTotal: 8,
      createUser: '李四',
      createTime: '2024-02-20 14:00:00',
    },
    {
      taskNo: 'TASK003',
      productModelNames: 'ModelC,ModelE,ModelA',
      blockNames: 'Block3',
      taskStatus: 3,
      taskStatusName: '已取消',
      successTotal: 5,
      failureTotal: 5,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-05-25 10:30:00',
      deviceTotal: 10,
      createUser: '王五',
      createTime: '2024-05-15 10:30:00',
    },
    {
      taskNo: 'TASK004',
      productModelNames: 'ModelD',
      blockNames: 'Block5',
      taskStatus: 4,
      taskStatusName: '创建失败',
      successTotal: 0,
      failureTotal: 0,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-11-01 08:00:00',
      deviceTotal: 0,
      createUser: '赵六',
      createTime: '2024-11-01 08:00:00',
    },
    {
      taskNo: 'TASK005',
      productModelNames: 'ModelE',
      blockNames: 'Block1',
      taskStatus: 0,
      taskStatusName: '创建中',
      successTotal: 0,
      failureTotal: 0,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-04-02 16:45:00',
      deviceTotal: 0,
      createUser: '陈七',
      createTime: '2024-03-28 16:45:00',
    },
    {
      taskNo: 'TASK006',
      productModelNames: 'ModelA,ModelC',
      blockNames: 'Block2,Block3',
      taskStatus: 2,
      taskStatusName: '已生效',
      successTotal: 7,
      failureTotal: 3,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-08-12 11:20:00',
      deviceTotal: 10,
      createUser: '张三',
      createTime: '2024-08-12 11:20:00',
    },
    {
      taskNo: 'TASK007',
      productModelNames: 'ModelB',
      blockNames: 'Block4',
      taskStatus: 1,
      taskStatusName: '待生效',
      successTotal: 10,
      failureTotal: 5,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-09-01 00:00:00',
      deviceTotal: 15,
      createUser: '李四',
      createTime: '2024-08-25 00:00:00',
    },
    {
      taskNo: 'TASK008',
      productModelNames: 'ModelD,ModelE',
      blockNames: 'Block5',
      taskStatus: 2,
      taskStatusName: '已生效',
      successTotal: 18,
      failureTotal: 2,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-06-15 14:10:00',
      deviceTotal: 20,
      createUser: '王五',
      createTime: '2024-06-15 14:10:00',
    },
    {
      taskNo: 'TASK009',
      productModelNames: 'ModelC',
      blockNames: 'Block1',
      taskStatus: 3,
      taskStatusName: '已取消',
      successTotal: 0,
      failureTotal: 8,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-10-10 09:00:00',
      deviceTotal: 8,
      createUser: '赵六',
      createTime: '2024-10-01 09:00:00',
    },
    {
      taskNo: 'TASK010',
      productModelNames: 'ModelA,ModelB,ModelC',
      blockNames: 'Block2',
      taskStatus: 4,
      taskStatusName: '创建失败',
      successTotal: 0,
      failureTotal: 0,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-12-05 17:30:00',
      deviceTotal: 0,
      createUser: '陈七',
      createTime: '2024-12-05 17:30:00',
    },
    {
      taskNo: 'TASK011',
      productModelNames: 'ModelE',
      blockNames: 'Block3,Block4',
      taskStatus: 2,
      taskStatusName: '已生效',
      successTotal: 9,
      failureTotal: 1,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-04-20 13:45:00',
      deviceTotal: 10,
      createUser: '张三',
      createTime: '2024-04-20 13:45:00',
    },
    {
      taskNo: 'TASK012',
      productModelNames: 'ModelD',
      blockNames: 'Block5',
      taskStatus: 1,
      taskStatusName: '待生效',
      successTotal: 6,
      failureTotal: 4,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-07-22 08:00:00',
      deviceTotal: 10,
      createUser: '李四',
      createTime: '2024-07-15 08:00:00',
    },
    {
      taskNo: 'TASK013',
      productModelNames: 'ModelA,ModelE',
      blockNames: 'Block1',
      taskStatus: 0,
      taskStatusName: '创建中',
      successTotal: 0,
      failureTotal: 0,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-05-01 12:00:00',
      deviceTotal: 0,
      createUser: '王五',
      createTime: '2024-04-25 12:00:00',
    },
    {
      taskNo: 'TASK014',
      productModelNames: 'ModelB,ModelC',
      blockNames: 'Block2',
      taskStatus: 2,
      taskStatusName: '已生效',
      successTotal: 15,
      failureTotal: 5,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-09-10 10:10:10',
      deviceTotal: 20,
      createUser: '赵六',
      createTime: '2024-09-10 10:10:10',
    },
    {
      taskNo: 'TASK015',
      productModelNames: 'ModelD',
      blockNames: 'Block3',
      taskStatus: 3,
      taskStatusName: '已取消',
      successTotal: 0,
      failureTotal: 7,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-11-15 15:30:00',
      deviceTotal: 7,
      createUser: '陈七',
      createTime: '2024-11-10 15:30:00',
    },
    {
      taskNo: 'TASK016',
      productModelNames: 'ModelA,ModelB,ModelD',
      blockNames: 'Block4',
      taskStatus: 4,
      taskStatusName: '创建失败',
      successTotal: 0,
      failureTotal: 0,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-02-14 09:45:00',
      deviceTotal: 0,
      createUser: '张三',
      createTime: '2024-02-14 09:45:00',
    },
    {
      taskNo: 'TASK017',
      productModelNames: 'ModelC',
      blockNames: 'Block5',
      taskStatus: 2,
      taskStatusName: '已生效',
      successTotal: 8,
      failureTotal: 2,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-06-01 18:00:00',
      deviceTotal: 10,
      createUser: '李四',
      createTime: '2024-06-01 18:00:00',
    },
    {
      taskNo: 'TASK018',
      productModelNames: 'ModelE',
      blockNames: 'Block1,Block2',
      taskStatus: 1,
      taskStatusName: '待生效',
      successTotal: 4,
      failureTotal: 1,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-08-08 08:08:08',
      deviceTotal: 5,
      createUser: '王五',
      createTime: '2024-08-01 08:08:08',
    },
    {
      taskNo: 'TASK019',
      productModelNames: 'ModelA',
      blockNames: 'Block3',
      taskStatus: 0,
      taskStatusName: '创建中',
      successTotal: 0,
      failureTotal: 0,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-12-25 20:00:00',
      deviceTotal: 0,
      createUser: '赵六',
      createTime: '2024-12-20 20:00:00',
    },
    {
      taskNo: 'TASK020',
      productModelNames: 'ModelB,ModelE',
      blockNames: 'Block4',
      taskStatus: 2,
      taskStatusName: '已生效',
      successTotal: 10,
      failureTotal: 0,
      immediately: 1,
      immediatelyName: '立即生效',
      issueTime: '2024-10-10 10:10:10',
      deviceTotal: 10,
      createUser: '陈七',
      createTime: '2024-10-10 10:10:10',
    },
    {
      taskNo: 'TASK021',
      productModelNames: 'ModelC,ModelD',
      blockNames: 'Block5',
      taskStatus: 3,
      taskStatusName: '已取消',
      successTotal: 0,
      failureTotal: 6,
      immediately: 0,
      immediatelyName: '定时生效',
      issueTime: '2024-04-18 14:20:00',
      deviceTotal: 6,
      createUser: '张三',
      createTime: '2024-04-10 14:20:00',
    },
  ],
};

class ConfigTaskManageApi {
  public getDeviceConfigIssueTaskPage = (params: {
    pageNum: number;
    pageSize: number;
    blockNo: string;
    taskStatus: number;
    taskNo: string;
    createTimeStart: string;
    createTimeEnd: string;
    createUser: string;
    productKey: string;
    productModelNo: string;
  }): Promise<any> => {
    const requestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_issue_task/get_device_config_issue_task_page',
      body: params,
      newGeteway: true,
    };
    // return request(requestOptions);
    return Promise.resolve({
      code: '0000',
      data: listData,
      message: 'ok',
    });
  };
  public createDeviceConfigIssueTask = (params: any) => {
    const requestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_issue_task/create_device_config_issue_task',
      body: params,
      newGeteway: true,
    };
    // return request(requestOptions);
    return Promise.resolve({
      code: '0000',
      data: false,
      message: 'ok',
    });
  };
  public getDeviceConfigIssueTask = (params: any) => {
    const requestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_template/get_device_config_issue_task',
      body: params,
      newGeteway: true,
    };
    // return request(requestOptions);
    return Promise.resolve({
      code: '0000',
      data: mockDataList,
      message: 'ok',
    });
  };
  public getDeviceConfigDevicePage = (params: any) => {
    const requestOptions = {
      method: 'POST',
      path: '/intelligent/device/web/config_template/get_device_config_device_page',
      body: params,
      newGeteway: true,
    };
    // return request(requestOptions);
    return Promise.resolve({
      code: '0000',
      data: null,
      message: 'ok',
    });
  };
}

export default ConfigTaskManageApi;
