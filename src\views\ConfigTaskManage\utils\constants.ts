import { FormConfig } from '@jd/x-coreui/es/components/CommonForm';
import { Device } from '@/fetch/bussiness';
import { HttpStatusCode } from '@/fetch/core/constant';
const deviceApi = new Device();

export enum TaskStatus {
  CREATING = 0, // 创建中
  TO_BE_EFFECTIVE = 1, // 待生效
  EFFECTIVE = 2, // 已生效
  CANCELED = 3, // 已取消
  CREATE_FAILED = 4, // 创建失败
}

export enum DeviceChoiceTypeMap {
  DIRECTIONAL = 0,
  UPLOAD = 1,
  CONDITIONAL = 2,
}

export enum PushType {
  IMMEDIATELEY = 1,
  TIME = 0,
}

export enum BatchUpgrade {
  YES = 1,
  NO = 0,
}

export const SearchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '产品',
      type: 'select',
      labelInValue: false,
      placeholder: '请选择产品',
      options: [], // TODO，默认选中PDA还没有写
      validatorRules: [
        {
          required: true,
          message: '请选择产品',
        },
      ],
    },
    {
      fieldName: 'blockNo',
      label: '模块',
      type: 'select',
      labelInValue: false,
      placeholder: '请选择模块',
      options: [],
    },
    {
      fieldName: 'productModelNo',
      label: '型号',
      type: 'select',
      labelInValue: false,
      placeholder: '请选择型号',
      options: [],
    },
    {
      fieldName: 'taskNo',
      label: '任务编号',
      type: 'input',
      placeholder: '请选择任务编号',
    },
    {
      fieldName: 'taskStatus',
      label: '任务状态',
      type: 'select',
      labelInValue: false,
      placeholder: '请选择任务编号',
      options: [
        {
          value: 0,
          label: '创建中',
        },
        {
          value: 1,
          label: '待生效',
        },
        {
          value: 2,
          label: '已生效',
        },
        {
          value: 3,
          label: '已取消',
        },
        {
          value: 4,
          label: '创建失败',
        },
      ],
    },
    {
      fieldName: 'createUser',
      label: '创建人',
      type: 'input',
      placeholder: '请选择创建人',
    },
    {
      fieldName: 'createTimeRange',
      label: '创建时间',
      type: 'rangeTime',
      placeholder: '请选择时间范围',
    },
  ],
};

export const TableColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    align: 'center',
    width: 70,
  },
  {
    title: '任务编号',
    dataIndex: 'taskNo',
    align: 'center',
    width: 70,
  },
  {
    title: '模块',
    dataIndex: 'blockNames',
    align: 'center',
    width: 70,
  },
  {
    title: '产品型号',
    dataIndex: 'productModelNames',
    align: 'center',
    width: 70,
  },
  {
    title: '任务状态',
    dataIndex: 'taskStatusName',
    align: 'center',
    width: 70,
  },
  {
    title: '成功/未成功',
    dataIndex: 'successOrFailed',
    align: 'center',
    width: 70,
  },
  {
    title: '生效时间设置',
    dataIndex: 'immediatelyName',
    align: 'center',
    width: 70,
  },
  {
    title: '生效时间',
    dataIndex: 'issueTime',
    align: 'center',
    width: 70,
  },
  {
    title: '发布数量',
    dataIndex: 'deviceTotal',
    align: 'center',
    width: 70,
  },
  {
    title: '创建人',
    dataIndex: 'createUser',
    align: 'center',
    width: 70,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    align: 'center',
    width: 70,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 100,
    fixed: 'right',
    align: 'center',
  },
];

export const DeviceEditFormConfig: FormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '产品型号选择',
      type: 'select',
      placeholder: '请选择产品',
      labelInValue: false,
      options: [],
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 12,
      xxl: 12,
      validatorRules: [
        {
          required: true,
          message: '请选择产品',
        },
      ],
    },
    {
      fieldName: 'productModelNoList',
      label: '',
      type: 'select',
      multiple: true,
      labelInValue: false,
      placeholder: '请选择型号',
      options: [],
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xl: 12,
      xxl: 12,
      validatorRules: [
        {
          required: true,
          message: '请选择型号',
        },
      ],
    },
  ],
  linkRules: {
    productKey: [
      {
        linkFieldName: 'productModelNoList',
        rule: 'clear',
      },
      {
        linkFieldName: 'productModelNoList',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) {
            return [];
          }
          const res = await deviceApi.queryModelList(val);
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.modelName,
              value: item.modelNo,
            }));
          } else {
            return [];
          }
        },
      },
    ],
  },
};

export const RulesConfigurationFormConfig: FormConfig = {
  fields: [
    {
      fieldName: 'immediately',
      label: '推送时间',
      type: 'radioGroup',
      options: [
        {
          label: '立即推送',
          value: PushType.IMMEDIATELEY,
        },
        {
          label: '定时推送',
          value: PushType.TIME,
        },
      ],
      validatorRules: [
        {
          required: true,
          message: '请选择推送时间',
        },
      ],
      labelCol: { span: 16 },
      wrapperCol: { span: 8 },
      xl: 12,
      xxl: 12,
    },
    {
      fieldName: 'executionTime',
      label: '',
      type: 'dateTime',
      hidden: true,
      validatorRules: [
        {
          required: true,
          message: '请选择日期时间',
        },
      ],
      wrapperCol: { span: 12 },
      xl: 12,
      xxl: 12,
    },
    {
      fieldName: 'batch',
      label: '是否分批执行',
      type: 'radioGroup',
      options: [
        {
          label: '否',
          value: BatchUpgrade.NO,
        },
        {
          label: '是',
          value: BatchUpgrade.YES,
        },
      ],
      validatorRules: [
        {
          required: true,
          message: '请选择是否分批执行',
        },
      ],
      labelCol: { span: 16 },
      wrapperCol: { span: 8 },
      xl: 13,
      xxl: 13,
    },
    {
      fieldName: 'batchInterval',
      label: '批次间隔',
      type: 'select',
      labelInValue: false,
      options: Array.from({ length: 24 }, (_, i) => ({
        label: `${i + 1}小时`,
        value: (i + 1) * 60,
      })),
      hidden: true,
      validatorRules: [
        {
          required: true,
          message: '请选择批次间隔',
        },
      ],
      labelCol: { span: 10 },
      wrapperCol: { span: 14 },
      xl: 4,
      xxl: 4,
    },
    {
      fieldName: 'batchCount',
      label: '单次推送数量',
      type: 'inputNumber',
      hidden: true,
      min: 0,
      max: 10000,
      placeholder: '请输入0-10000之间整数',
      validatorRules: [
        {
          required: true,
          message: '请输入0-10000之间整数',
        },
      ],
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
      xl: 7,
      xxl: 7,
    },
  ],
  linkRules: {
    immediately: [
      {
        linkFieldName: 'executionTime',
        rule: 'visible',
        dependenceData: [PushType.TIME],
      },
    ],
    batch: [
      {
        linkFieldName: 'batchInterval',
        rule: 'visible',
        dependenceData: [BatchUpgrade.YES],
      },
      {
        linkFieldName: 'batchCount',
        rule: 'visible',
        dependenceData: [BatchUpgrade.YES],
      },
    ],
  },
};

export const detailFormConfig: FormConfig = {
  fields: [
    {
      fieldName: 'taskNo',
      label: '任务编号',
      type: 'text',
    },
    {
      fieldName: 'effectiveTime',
      label: '生效时间',
      type: 'text',
    },
    {
      fieldName: 'createUser',
      label: '创建人',
      type: 'text',
    },
    {
      fieldName: 'taskStatusName',
      label: '任务状态',
      type: 'text',
    },
    {
      fieldName: 'immediately',
      label: '推送时间',
      type: 'radioGroup',
      disabled: true,
      options: [
        {
          label: '立即推送',
          value: PushType.IMMEDIATELEY,
        },
        {
          label: '定时推送',
          value: PushType.TIME,
        },
      ],
    },
    {
      fieldName: 'executionTime',
      label: '定时时间',
      type: 'text',
    },
    {
      fieldName: 'createTime',
      label: '创建时间',
      type: 'text',
    },
    {
      fieldName: 'batch',
      label: '是否分批执行',
      type: 'radioGroup',
      disabled: true,
      options: [
        {
          label: '否',
          value: BatchUpgrade.NO,
        },
        {
          label: '是',
          value: BatchUpgrade.YES,
        },
      ],
    },
    {
      fieldName: 'batchInterval',
      label: '间隔时间',
      type: 'text',
    },
    {
      fieldName: 'batchCount',
      label: '单次推送数量',
      type: 'text',
    },
  ],
  linkRules: {
    immediately: [
      {
        linkFieldName: 'executionTime',
        rule: 'visible',
        dependenceData: [PushType.TIME],
      },
      {
        linkFieldName: 'createTime',
        rule: 'visible',
        dependenceData: [PushType.IMMEDIATELEY],
      },
    ],
    batch: [
      {
        linkFieldName: 'batchInterval',
        rule: 'visible',
        dependenceData: [BatchUpgrade.YES],
      },
      {
        linkFieldName: 'batchCount',
        rule: 'visible',
        dependenceData: [BatchUpgrade.YES],
      },
    ],
  },
};

export const detailSearchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '产品',
      type: 'select',
      labelInValue: false,
      disabled: true,
      placeholder: '请选择产品',
      options: [], // TODO，默认选中PDA还没有写
    },
    {
      fieldName: 'productModelNo',
      label: '型号',
      type: 'select',
      labelInValue: false,
      placeholder: '请选择型号',
      options: [],
    },
    {
      fieldName: 'executeStatus',
      label: '推送结果',
      type: 'select',
      labelInValue: false,
      placeholder: '请选择推送结果',
      options: [],
    },
    {
      fieldName: 'deviceName',
      label: '设备名称',
      type: 'input',
      placeholder: '请输入设备名称',
    },
  ],
};
