import { FormConfig } from '@/components/CommonForm/formConfig';
import { HttpStatusCode } from '@/fetch/core/constant';
import {
  getTaskStatus,
  Device,
  getAppVersionListByModel,
  getAppTypeList,
  getAppTypeListByType,
  FirmwareFetch,
} from '@/fetch/bussiness';
import { NULL_GROUP, formatTreeData, isEmpty } from '@/utils/utils';
const deviceApi = new Device();

export enum DeviceChoiceTypeMap {
  DIRECTIONAL = 0,
  UPLOAD = 1,
  CONDITIONAL = 2,
}

export const StepEditFormConfig: FormConfig = {
  fields: [
    {
      fieldName: 'deviceChoiceType',
      type: 'radioGroup',
      label: '设备选择',
      options: [
        {
          label: '定向选择',
          value: DeviceChoiceTypeMap.DIRECTIONAL,
        },
        {
          label: '文件上传',
          value: DeviceChoiceTypeMap.UPLOAD,
        },
        {
          label: '条件选中',
          value: DeviceChoiceTypeMap.CONDITIONAL,
        },
      ],
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xl: 24,
      xxl: 24,
      validatorRules: [
        {
          required: true,
          message: '请选择设备',
        },
      ],
    },
  ],
};

export const DeviceTableColumns = [
  {
    title: '序号',
    dataIndex: 'order',
    align: 'center',
    width: 100,
  },
  {
    title: '型号',
    dataIndex: 'productModelName',
    align: 'center',
  },
  {
    title: '分组',
    dataIndex: 'groupLevelName',
    align: 'center',
  },
  {
    title: '设备名称',
    dataIndex: 'deviceName',
    align: 'center',
  },
  {
    title: '设备状态',
    dataIndex: 'isOnlineName',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 100,
    fixed: 'right',
  },
];

export const DeviceSearchForm: FormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '产品',
      type: 'select',
      labelInValue: false,
      disabled: true,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      xl: 8,
      xxl: 8,
    },
    {
      fieldName: 'productModelNoList',
      label: '型号',
      type: 'select',
      labelInValue: false,
      disabled: true,
      multiple: true,
      xl: 8,
      xxl: 8,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
    },
    {
      fieldName: 'groupNoList',
      label: '设备分组',
      type: 'cascader',
      placeholder: '请选择',
      labelInValue: false,
      multiple: true,
      maxTagCount: 1,
      xl: 8,
      xxl: 8,
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
    },
    {
      fieldName: 'appType',
      label: '升级包依赖',
      type: 'select',
      labelInValue: false,
      placeholder: '升级包类型',
      xl: 8,
      xxl: 8,
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
    },
    {
      fieldName: 'appName',
      label: '',
      type: 'select',
      labelInValue: false,
      placeholder: '升级包名称',
      xl: 6,
      xxl: 6,
      wrapperCol: { span: 24 },
    },
    {
      fieldName: 'appVersionNumber',
      label: '',
      type: 'select',
      labelInValue: false,
      placeholder: '版本号',
      xl: 6,
      xxl: 6,
      wrapperCol: { span: 24 },
    },
    {
      fieldName: 'deviceName',
      label: '设备名称',
      type: 'input',
      placeholder: '请输入',
      xl: 8,
      xxl: 8,
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
    },
  ],
  linkRules: {
    fetchProductKey: [
      {
        linkFieldName: 'productKey',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          const res = await deviceApi.queryProductList();
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.productName,
              value: item.productKey,
            }));
          } else {
            return [];
          }
        },
      },
    ],
    productKey: [
      {
        linkFieldName: 'groupNoList',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) {
            return [];
          }
          const res = await deviceApi.getAllGroupList({
            productKey: val,
          });
          if (res.code === HttpStatusCode.Success) {
            const groupData = formatTreeData({
              origin: res?.data?.groupNoList,
              type: 'Cascader',
              level: 0,
              productKey: val,
            });
            groupData.unshift(NULL_GROUP);
            return groupData;
          } else {
            return [];
          }
        },
      },
      {
        linkFieldName: 'appType',
        rule: 'fetchData',
        fetchFunc: async (val: any, formInstance: any) => {
          if (!val) {
            return [];
          }
          const res = await getAppTypeList();
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.name,
              value: item.value,
            }));
          } else {
            return [];
          }
        },
      },
      {
        linkFieldName: 'productModelNoList',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) {
            return [];
          }
          const res = await deviceApi.queryModelList(val);
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.modelName,
              value: item.modelNo,
            }));
          } else {
            return [];
          }
        },
      },
    ],
    appType: [
      {
        linkFieldName: 'appName',
        rule: 'clear',
      },
      {
        linkFieldName: 'appName',
        rule: 'fetchData',
        fetchFunc: async (val: any, commonFormRef: any) => {
          if (!val) {
            return [];
          }
          const values = commonFormRef.getFieldsValue();
          const res = await getAppTypeListByType({
            productKey: values?.productKey,
            type: val,
          });
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.appAlias,
              value: item.appName,
            }));
          } else {
            return [];
          }
        },
      },
    ],
    appName: [
      {
        linkFieldName: 'appVersionNumber',
        rule: 'clear',
      },
      {
        linkFieldName: 'appVersionNumber',
        rule: 'fetchData',
        fetchFunc: async (val: any, commonFormRef: any) => {
          if (!val) {
            return [];
          }
          const values = commonFormRef.getFieldsValue();
          const res = await getAppVersionListByModel({
            productKey: values?.productKey,
            appName: values?.appName,
            type: values?.appType,
            enable: 1,
            productModelNoList: values?.productModelNoList,
          });
          if (res.code === HttpStatusCode.Success) {
            return res.data?.map((item: any) => ({
              label: item.appVersion,
              value: item.appVersionNumber,
            }));
          } else {
            return [];
          }
        },
      },
    ],
  },
};
