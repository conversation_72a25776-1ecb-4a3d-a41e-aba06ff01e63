import React, { useEffect } from 'react';
import BreadCrumb from '@/components/BreadCrumb';
import ConfigTaskManageApi from '@/fetch/bussiness/configTaskManage';
import { HttpStatusCode } from '@/fetch/core/constant';
import { formatLocation } from '@/utils/formatLocation';
import { message } from 'antd';
import { CommonForm, CommonTable } from '@jd/x-coreui';
import { detailFormConfig, detailSearchConfig } from '../utils/constants';

const configTaskManageApi = new ConfigTaskManageApi();

function ConfigTaskDetail() {
  const { taskNo } = formatLocation(window.location.search);
  const initSearchCondition = {
    searchForm: {
      productModelNoList: null,
      executeStatus: null,
      deviceName: null,
      taskNo: taskNo,
      detailNo: taskNo, // TODO待确认这个字段
    },
    pageNum: 1,
    pageSize: 10,
  };
  const [searchCondition, setSearchCondition] =
    React.useState<any>(initSearchCondition);
  const [taskDetail, setTaskDetail] = React.useState<any>(null);

  const breadCrumbList = [
    {
      title: '通用设备管理',
      route: '',
    },
    {
      title: '配置任务管理',
      route: '/ota/configTaskManage',
    },
    {
      title: '任务详情',
      route: '',
    },
  ];

  useEffect(() => {
    if (!taskNo) {
      message.error('任务号不能为空');
      return;
    }
    try {
      configTaskManageApi.getDeviceConfigIssueTask({ taskNo }).then((res) => {
        if (res.code === HttpStatusCode.Success) {
          setTaskDetail(res?.data || {});
        } else {
          message.error(res.message || '获取任务详情失败');
        }
      });
    } catch (e) {
      console.log(e);
    }
  }, []);

  useEffect(() => {
    
  }, []);

  return (
    <>
      <BreadCrumb items={breadCrumbList} />
      <CommonForm
        formConfig={detailFormConfig}
        defaultValue={taskDetail}
        layout="inline"
      />
      <CommonForm formConfig={detailSearchConfig} formType="search" />
      <CommonTable searchCondition={searchCondition} />
    </>
  );
}

export default ConfigTaskDetail;
