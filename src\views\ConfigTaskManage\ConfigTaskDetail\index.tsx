import React, { useState, useEffect, useMemo } from 'react';
import BreadCrumb from '@/components/BreadCrumb';
import ConfigTaskManageApi from '@/fetch/bussiness/configTaskManage';
import { HttpStatusCode } from '@/fetch/core/constant';
import { formatLocation } from '@/utils/formatLocation';
import { message } from 'antd';
import { CommonForm, CommonTable, useTableData } from '@jd/x-coreui';
import {
  detailFormConfig,
  detailSearchConfig,
  detailTableColumns,
} from '../utils/constants';
import './index.scss';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import {
  saveSearchValues,
  removeSearchValues,
} from '@/redux/reducers/searchform';

const configTaskManageApi = new ConfigTaskManageApi();
// 这个页面待确认：
// 为什么要加勾选框？没有收集数据的设计
function ConfigTaskDetail() {
  const { taskNo } = formatLocation(window.location.search);
  const initSearchCondition = {
    searchForm: {
      productModelNoList: null,
      executeStatus: null,
      deviceName: null,
      taskNo: taskNo,
      detailNo: taskNo, // TODO待确认这个字段
    },
    pageNum: 1,
    pageSize: 10,
  };
  const dispatch = useDispatch();
  const historySearchValues = useSelector(
    (state: RootState) => state.searchform,
  );
  const [searchCondition, setSearchCondition] = useState<any>(() => {
    return historySearchValues.searchValues
      ? historySearchValues.searchValues
      : initSearchCondition;
  });
  const [taskDetail, setTaskDetail] = useState<any>(null);

  const { tableData, loading, reloadTable } = useTableData(
    {
      ...searchCondition.searchForm,
      pageNum: searchCondition.pageNum,
      pageSize: searchCondition.pageSize,
    },
    configTaskManageApi.getDeviceConfigDevicePage,
  );

  const breadCrumbList = [
    {
      title: '通用设备管理',
      route: '',
    },
    {
      title: '配置任务管理',
      route: '/ota/configTaskManage',
    },
    {
      title: '任务详情',
      route: '',
    },
  ];

  const onSearchClick = (values: any) => {
    const newSearchCondition = {
      searchForm: values,
      pageNum: 1,
      pageSize: 10,
    };
    setSearchCondition(newSearchCondition);
    dispatch(
      saveSearchValues({
        routeName: location.pathname,
        searchValues: newSearchCondition,
      }),
    );
  };

  const onResetClick = () => {
    setSearchCondition(initSearchCondition);
    dispatch(
      removeSearchValues({
        routeName: null,
        searchValues: initSearchCondition,
      }),
    );
  };

  const formatTableColumns = useMemo(() => {
    return detailTableColumns?.map((col: any) => {
      switch (col.dataIndex) {
        case 'index':
          col.render = (text: any, record: any, index: number) =>
            `${
              (searchCondition.pageNum - 1) * searchCondition.pageSize +
              index +
              1
            }`;
          break;
        case 'operation':
          col.render = (text: any, record: any, index: number) => {
            return (
              <div className="operate-btn">
                <a>查看配置</a>
              </div>
            );
          };
          break;
        default:
          break;
      }
      return col;
    });
  }, [searchCondition.pageNum, searchCondition.pageSize]);

  useEffect(() => {
    if (!taskNo) {
      message.error('任务号不能为空');
      return;
    }
    try {
      configTaskManageApi.getDeviceConfigIssueTask({ taskNo }).then((res) => {
        if (res.code === HttpStatusCode.Success) {
          setTaskDetail(res?.data || {});
        } else {
          message.error(res.message || '获取任务详情失败');
        }
      });
    } catch (e) {
      console.log(e);
    }
  }, []);

  return (
    <div className="config-task-detail">
      <BreadCrumb items={breadCrumbList} />
      <CommonForm
        formConfig={detailFormConfig}
        defaultValue={taskDetail}
        layout="inline"
      />
      <div className="search-area-container">
        <CommonForm
          formConfig={detailSearchConfig}
          formType="search"
          layout="inline"
        />
        <CommonTable
          searchCondition={searchCondition}
          tableListData={{
            list: tableData?.list || [],
            totalPage: tableData?.pages,
            totalNumber: tableData?.total,
          }}
          loading={loading}
          columns={formatTableColumns}
          onPageChange={(paginationData: any) => {
            setSearchCondition({
              ...searchCondition,
              pageNum: paginationData.pageNum,
              pageSize: paginationData.pageSize,
            });
          }}
        />
      </div>
    </div>
  );
}

export default ConfigTaskDetail;
