import { FormConfig } from '@/components/CommonForm/formConfig';
import { HttpStatusCode } from '@/fetch/core/constant';
import { TSLModelFetch, Device, CommandControlFetch } from '@/fetch/bussiness';
import { FormInstance } from 'antd';
import { formatTreeData, NULL_GROUP } from '@/utils/utils';
import dayjs from 'dayjs';

const deviceApi = new Device();
const tslFetch = new TSLModelFetch();
const commandApi = new CommandControlFetch();
export const TableConfig: any[] = [
  {
    title: '序号',
    dataIndex: 'order',
    align: 'center',
    width: 60,
    fixed: 'left',
  },
  {
    title: '任务编号',
    width: 100,
    dataIndex: 'taskNo',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '产品型号',
    width: 90,
    dataIndex: 'productModelNo',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '模块',
    width: 120,
    dataIndex: 'blockName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '指令名称',
    width: 90,
    dataIndex: 'serviceName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '指令内容',
    width: 130,
    dataIndex: 'commandContent',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '任务状态',
    width: 90,
    dataIndex: 'taskStatusName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '成功/未成功',
    width: 120,
    dataIndex: 'result',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '生效时间设置',
    width: 120,
    dataIndex: 'effectiveTypeName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '生效时间',
    width: 130,
    dataIndex: 'effectiveTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '发布数量',
    width: 90,
    dataIndex: 'total',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建人',
    width: 130,
    dataIndex: 'createUser',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    width: 150,
    dataIndex: 'createTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    align: 'center',
    width: 150,
    fixed: 'right',
  },
];

export const DeviceTableConfig: any[] = [
  { title: '序号', dataIndex: 'order', align: 'center', width: 70 },
  {
    title: '型号',
    width: 150,
    dataIndex: 'productModelName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '分组',
    width: 90,
    dataIndex: 'groupLevelName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '设备名称',
    width: 220,
    dataIndex: 'deviceName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '设备状态',
    width: 220,
    dataIndex: 'isOnlineName',
    align: 'center',
    ellipsis: true,
  },
];

export const CheckTaskDeviceTableConfig: any[] = [
  {
    title: '序号',
    dataIndex: 'order',
    align: 'center',
    width: 60,
    fixed: 'left',
  },
  {
    title: '唯一标识',
    width: 160,
    dataIndex: 'detailNo',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '分组',
    width: 90,
    dataIndex: 'groupLevelName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '设备名称',
    width: 100,
    dataIndex: 'deviceName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '型号',
    width: 90,
    dataIndex: 'productModelName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '推送结果',
    width: 100,
    dataIndex: 'executeStatusName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '在线状态',
    width: 80,
    dataIndex: 'onlineName',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '最后更新时间',
    width: 120,
    dataIndex: 'modifyTime',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '备注',
    width: 120,
    dataIndex: 'remark',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '是否终止',
    width: 90,
    dataIndex: 'stopName',
    align: 'center',
    ellipsis: true,
  },
];

export const SearchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '产品',
      placeholder: '请选择产品',
      type: 'select',
      options: [],
      allowClear: false,
      showSearch: true,
    },
    {
      fieldName: 'productModelNo',
      label: '型号',
      placeholder: '请选择产品型号',
      type: 'select',
      showSearch: true,
    },
    {
      fieldName: 'blockNo',
      label: '模块',
      placeholder: '请选择模块',
      type: 'select',
      showSearch: true,
    },
    {
      fieldName: 'identifier',
      label: '指令名称',
      placeholder: '请选择指令名称',
      type: 'select',
      showSearch: true,
    },
    {
      fieldName: 'taskNo',
      label: '任务编号',
      placeholder: '请输入任务编号',
      type: 'input',
    },
    {
      fieldName: 'commandContentLike',
      label: '指令内容',
      placeholder: '请输入指令内容',
      type: 'input',
    },
    {
      fieldName: 'taskStatus',
      label: '任务状态',
      placeholder: '请选择任务状态',
      type: 'select',
      showSearch: true,
    },
    {
      fieldName: 'createUser',
      label: '创建人',
      placeholder: '请输入创建人账号',
      type: 'input',
    },
    {
      fieldName: 'createTime',
      label: '创建时间',
      type: 'rangeTime',
      xxl: 8,
      xl: 12,
      lg: 16,
      labelCol: { span: 3 },
      wrapperCol: { span: 20 },
    },
  ],
  linkRules: {
    productKey: [
      {
        linkFieldName: 'productModelNo',
        rule: 'clear',
      },
      {
        linkFieldName: 'blockNo',
        rule: 'clear',
      },
      {
        linkFieldName: 'identifier',
        rule: 'clear',
      },
      {
        linkFieldName: 'productModelNo',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) {
            return [];
          }
          const res = await deviceApi.queryModelList(val.value);
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.modelName,
              value: item.modelNo,
            }));
          } else {
            return [];
          }
        },
      },
      {
        linkFieldName: 'blockNo',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) {
            return [];
          }
          const res = await tslFetch.getThingModelBlocks({
            productKey: val.value,
          });
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.blockName,
              value: item.blockNo,
            }));
          } else {
            return [];
          }
        },
      },
    ],
    blockNo: [
      {
        linkFieldName: 'identifier',
        rule: 'clear',
      },
      {
        linkFieldName: 'identifier',
        rule: 'fetchData',
        fetchFunc: async (val: any, formInstance: FormInstance) => {
          if (!val) {
            return [];
          }
          const productKey = formInstance.getFieldValue('productKey');
          const res = await commandApi.getServerList({
            productKey: productKey.value,
            blockNo: val.value,
          });
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.name,
              value: item.identifier,
            }));
          } else {
            return [];
          }
        },
      },
    ],
  },
};

export const SelectInstructionConfig: FormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '产品',
      placeholder: '请选择产品',
      type: 'select',
      options: [],
      showSearch: true,
      labelInValue: false,
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      validatorRules: [
        {
          required: true,
          message: '请选择产品',
        },
      ],
    },
    {
      fieldName: 'blockNo',
      label: '模块',
      placeholder: '请选择模块',
      type: 'select',
      showSearch: true,
      labelInValue: false,
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      validatorRules: [
        {
          required: true,
          message: '请选择模块',
        },
      ],
    },
    {
      fieldName: 'identifier',
      label: '指令名称',
      placeholder: '请选择指令名称',
      type: 'select',
      showSearch: true,
      labelInValue: false,
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      validatorRules: [
        {
          required: true,
          message: '请选择指令名称',
        },
      ],
    },
    {
      fieldName: 'debugModel',
      label: '是否为研发调试模式',
      type: 'radioGroup',
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      options: [
        {
          value: 0,
          label: '否',
        },
        {
          value: 1,
          label: '是',
        },
      ],
      validatorRules: [
        {
          required: true,
          message: '请选择是否为研发调试模式',
        },
      ],
    },
  ],
  linkRules: {
    productKey: [
      {
        linkFieldName: 'blockNo',
        rule: 'clear',
      },
      {
        linkFieldName: 'identifier',
        rule: 'clear',
      },
      {
        linkFieldName: 'blockNo',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) {
            return [];
          }
          const res = await tslFetch.getThingModelBlocks({
            productKey: val,
          });
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.blockName,
              value: item.blockNo,
            }));
          } else {
            return [];
          }
        },
      },
    ],
    blockNo: [
      {
        linkFieldName: 'identifier',
        rule: 'clear',
      },
      {
        linkFieldName: 'identifier',
        rule: 'fetchData',
        fetchFunc: async (val: any, formInstance: FormInstance) => {
          if (!val) {
            return [];
          }
          const productKey = formInstance.getFieldValue('productKey');
          const res = await commandApi.getServerList({
            productKey: productKey,
            blockNo: val,
          });
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.name,
              value: item.identifier,
            }));
          } else {
            return [];
          }
        },
      },
    ],
    debugModelDisable: [
      {
        linkFieldName: 'debugModel',
        rule: 'valueDisable',
        dependenceData: [true],
        disabledValue: [1, 0],
      },
    ],
  },
};

const updateLinkedFields = (productKey: any, groupList: any[]) => {
  const groupData = formatTreeData({
    origin: groupList,
    type: 'Cascader',
    level: 0,
    productKey,
  });
  groupData.unshift(NULL_GROUP);
  return groupData;
};
export const TaskSelectDevice: FormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '产品',
      placeholder: '请选择产品',
      type: 'select',
      disabled: true,
      labelInValue: false,
    },
    {
      fieldName: 'productModelNoList',
      label: '型号',
      placeholder: '请选择产品型号',
      type: 'select',
      showSearch: true,
      labelInValue: false,
      multiple: true,
    },
    {
      fieldName: 'groupNoList',
      label: '设备分组',
      placeholder: '请选择设备分组',
      type: 'cascader',
      showSearch: true,
      labelInValue: false,
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
    },
    {
      fieldName: 'deviceName',
      label: '设备名称',
      placeholder: '请输入设备名称',
      type: 'input',
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
    },
  ],
  linkRules: {
    productKey: [
      {
        linkFieldName: 'productModelNoList',
        rule: 'clear',
      },
      {
        linkFieldName: 'groupNoList',
        rule: 'clear',
      },
      {
        linkFieldName: 'productModelNoList',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) {
            return [];
          }
          const res = await deviceApi.queryModelList(val);
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.modelName,
              value: item.modelNo,
            }));
          } else {
            return [];
          }
        },
      },
      {
        linkFieldName: 'groupNoList',
        rule: 'fetchData',
        fetchFunc: async (val: any, formInstance: FormInstance) => {
          if (!val) {
            return [];
          }
          const res = await deviceApi.getAllGroupList({ productKey: val });
          if (res.code === HttpStatusCode.Success) {
            const allGroupList = res?.data?.groupNoList || [];
            return updateLinkedFields(val, allGroupList);
          } else {
            return [];
          }
        },
      },
    ],
  },
};

export const SelectStrategyConfig: FormConfig = {
  fields: [
    {
      fieldName: 'immediately',
      label: '是否立即推送',
      type: 'radioGroup',
      options: [
        {
          value: 1,
          label: '立即推送',
        },
        {
          value: 0,
          label: '定时推送',
        },
      ],
      showSearch: true,
      labelInValue: false,
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      validatorRules: [
        {
          required: true,
          message: '请选择推送时间',
        },
      ],
    },
    {
      label: '定时推送时间',
      fieldName: 'executionTimeContainer',
      childrenList: ['date', 'time'],
      labelCol: { span: 5 },
      wrapperCol: { span: 18 },
      hidden: true,
    },
    {
      fieldName: 'date',
      type: 'datePicker',
      placeholder: '请选择日期',
      marginLeft: 0,
      marginRight: 0,
      width: '49%',
      isChild: true,
      hidden: true,
      validatorRules: [
        {
          required: true,
          message: '请选择日期',
        },
      ],
      disabledDate: (current) => {
        return current && current < dayjs().startOf('day');
      },
    },
    {
      fieldName: 'time',
      type: 'timePicker',
      placeholder: '请选择时间',
      marginLeft: 0,
      marginRight: 0,
      width: '49%',
      isChild: true,
      showNow: false,
      hidden: true,
      validatorRules: [
        {
          required: true,
          message: '请选择时间',
        },
      ],
    },
    {
      fieldName: 'batch',
      label: '是否分批执行',
      type: 'radioGroup',
      showSearch: true,
      labelInValue: false,
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      options: [
        {
          value: 0,
          label: '否',
        },
        {
          value: 1,
          label: '是',
        },
      ],
      validatorRules: [
        {
          required: true,
          message: '请选择是否分批执行',
        },
      ],
    },
    {
      fieldName: 'batchInterval',
      label: '批次间隔',
      placeholder: '请选择批次间隔',
      type: 'select',
      showSearch: true,
      labelInValue: false,
      hidden: true,
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      validatorRules: [
        {
          required: true,
          message: '请选择批次间隔',
        },
      ],
      options: Array.from({ length: 24 }, (_, i) => ({
        label: `${i + 1}小时`,
        value: (i + 1) * 60,
      })),
    },
    {
      fieldName: 'batchCount',
      label: '单次推送数量',
      type: 'inputNumber',
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      hidden: true,
      placeholder: '请输入单次推送数量',
      validatorRules: [
        {
          required: true,
          message: '请输入单次推送数量',
        },
        {
          pattern: /^([1-9]|\d{2,3}|1000|10000)$/g,
          message: '请输入1～10000内的整数',
        },
      ],
    },
    {
      fieldName: 'retain',
      label: '是否保留任务',
      type: 'radioGroup',
      tooltip:
        '不保留：不保留下发到单台设备的指令消息，设备不在线则无法接收和执行指令;保留任务：保留时间内设备上线即可接收任务',
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      options: [
        {
          value: 0,
          label: '不保留',
        },
        {
          value: 1,
          label: '保留任务',
        },
      ],
      validatorRules: [
        {
          required: true,
          message: '请选择是否保留任务',
        },
      ],
    },
    {
      label: '保留时间',
      fieldName: 'retainTimeContainer',
      childrenList: ['retainTime', 'unit'],
      labelCol: { span: 5 },
      wrapperCol: { span: 18 },
      hidden: true,
    },
    {
      fieldName: 'retainTime',
      type: 'inputNumber',
      placeholder: '请输入保留时间',
      marginLeft: 0,
      marginRight: 0,
      width: '70%',
      isChild: true,
      hidden: true,
      validatorRules: [
        {
          required: true,
          message: '请输入保留时间',
        },
        {
          pattern: /^[1-9]\d*$/g,
          message: '请输入非0整数',
        },
      ],
    },
    {
      fieldName: 'unit',
      type: 'select',
      marginLeft: 0,
      marginRight: 0,
      width: '20%',
      allowClear: false,
      placeholder: '请选择单位',
      hidden: true,
      isChild: true,
      options: [
        { label: '秒', value: 'sec' },
        { label: '分钟', value: 'min' },
        { label: '小时', value: 'hour' },
        { label: '天', value: 'day' },
      ],
    },
  ],
  linkRules: {
    immediately: [
      {
        linkFieldName: 'date',
        rule: 'clear',
      },
      {
        linkFieldName: 'time',
        rule: 'clear',
      },
      {
        linkFieldName: 'executionTimeContainer',
        dependenceData: [0],
        rule: 'visible',
      },
    ],
    batch: [
      {
        linkFieldName: 'batchInterval',
        rule: 'clear',
      },
      {
        linkFieldName: 'batchCount',
        rule: 'clear',
      },
      {
        linkFieldName: 'batchInterval',
        rule: 'visible',
        dependenceData: [1],
      },
      {
        linkFieldName: 'batchCount',
        rule: 'visible',
        dependenceData: [1],
      },
    ],
    retain: [
      {
        linkFieldName: 'retainTimeContainer',
        dependenceData: [1],
        rule: 'visible',
      },
      {
        linkFieldName: 'retainTime',
        rule: 'clear',
      },
      {
        linkFieldName: 'unit',
        rule: 'refresh',
      },
    ],
    date: [
      {
        linkFieldName: 'time',
        rule: 'clear',
      },
    ],
  },
};

export const CheckTaskSelectDevice: FormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '产品',
      placeholder: '请选择产品',
      type: 'select',
      disabled: true,
    },
    {
      fieldName: 'productModelNoList',
      label: '型号',
      placeholder: '请选择型号',
      type: 'select',
      showSearch: true,
      labelInValue: false,
      multiple: true,
    },
    {
      fieldName: 'executeStatus',
      label: '推送结果',
      placeholder: '请选择任务状态',
      type: 'select',
      showSearch: true,
      labelInValue: false,
      labelCol: { span: 6 },
      wrapperCol: { span: 17 },
    },
    {
      fieldName: 'deviceName',
      label: '设备名称',
      placeholder: '请输入设备名称',
      type: 'input',
      labelCol: { span: 6 },
      wrapperCol: { span: 17 },
    },
  ],
  linkRules: {
    productKey: [
      {
        linkFieldName: 'productModelNoList',
        rule: 'clear',
      },
      {
        linkFieldName: 'productModelNoList',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) {
            return [];
          }
          const res = await deviceApi.queryModelList(val);
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.modelName,
              value: item.modelNo,
            }));
          } else {
            return [];
          }
        },
      },
    ],
  },
};
