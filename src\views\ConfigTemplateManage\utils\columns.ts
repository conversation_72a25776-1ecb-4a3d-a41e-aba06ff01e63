import { FormConfig } from '@jd/x-coreui/es/components/CommonForm';
import { HttpStatusCode } from '@/fetch/core/constant';
import {
  ConfigTemplateApi,
  ProductManageFetch,
  Device,
} from '@/fetch/bussiness';

const DeviceApi = new Device();
export const TableConfig = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 80,
    align: 'center',
  },
  { title: '模板编号', dataIndex: 'templateNo', width: 120, align: 'center' },
  { title: '模板名称', dataIndex: 'templateName', width: 120, align: 'center' },
  {
    title: '适用型号',
    dataIndex: 'productModelList',
    width: 120,
    align: 'center',
  },
  { title: '备注', dataIndex: 'remark', width: 160, align: 'center' },
  { title: '创建人', dataIndex: 'createUser', width: 100, align: 'center' },
  { title: '创建时间', dataIndex: 'createTime', width: 160, align: 'center' },
  { title: '更新时间', dataIndex: 'modifyTime', width: 160, align: 'center' },
  { title: '更新人', dataIndex: 'modifyUser', width: 100, align: 'center' },
  { title: '状态', dataIndex: 'enableName', width: 100, align: 'center' },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 300,
    fixed: 'right',
    align: 'center',
  },
];

export const SearchConfig: FormConfig = {
  fields: [
    {
      fieldName: 'productKey',
      label: '产品',
      type: 'select',
      placeholder: '请选择产品',
      options: [],
    },
    {
      fieldName: 'productModelNo',
      label: '型号',
      type: 'select',
      placeholder: '请选择型号',
      options: [],
    },
    {
      fieldName: 'templateNo',
      label: '配置模板编号',
      type: 'input',
      placeholder: '请输入配置模板编号',
    },
    {
      fieldName: 'templateName',
      label: '配置模板名称',
      type: 'input',
      placeholder: '请输入配置模板名称',
    },
    {
      fieldName: 'createUser',
      label: '创建人',
      type: 'input',
      placeholder: '请输入创建人账号',
    },
    {
      fieldName: 'enable',
      label: '状态',
      type: 'select',
      placeholder: '请选择状态',
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 },
      ],
    },
    {
      fieldName: 'createTime',
      label: '创建时间',
      type: 'rangeTime',
      xxl: 8,
      xl: 12,
      lg: 16,
      labelCol: { span: 3 },
      wrapperCol: { span: 20 },
    },
  ],
  linkRules: {
    productKey: [
      {
        linkFieldName: 'productModelNo',
        rule: 'clear',
      },
      {
        linkFieldName: 'productModelNo',
        rule: 'fetchData',
        fetchFunc: async (val: any) => {
          if (!val) return [];
          const res = await DeviceApi.queryModelList(val.value);
          if (res.code === HttpStatusCode.Success) {
            return res.data.map((item: any) => ({
              label: item.modelName,
              value: item.modelNo,
            }));
          }
          return [];
        },
      },
    ],
  },
};
